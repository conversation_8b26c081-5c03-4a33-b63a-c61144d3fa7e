//========= Copyright Valve Corporation, All rights reserved. ============//
//
// Purpose: 
//
// $NoKeywords: $
//=============================================================================//

#include <vgui_controls/VectorImagePanel.h>
#include "vgui_controls/Controls.h"
#include <vgui/ISurface.h>
#include <bitmap/bitmap.h>
#include <KeyValues.h>
#include "bitmap/imageformat.h"
#include "filesystem.h"
#include "VGuiMatSurface/IMatSystemSurface.h"

#ifndef _DEBUG
#include "lunasvg/lunasvg.h"
#endif

// memdbgon must be the last include file in a .cpp file!!!
#include <tier0/memdbgon.h>

using namespace vgui;

DECLARE_BUILD_FACTORY( VectorImagePanel );

//-----------------------------------------------------------------------------
// Purpose: Check box image
//-----------------------------------------------------------------------------
VectorImagePanel::VectorImagePanel( Panel *parent, const char *name ): Panel( parent, name )
{
	m_nTextureID = -1;
	m_iImageSize[0] = m_iImageSize[1] = 0;
	m_iPanelSize[0] = m_iPanelSize[1] = 0;
	m_iRepeatMargin[0] = m_iRepeatMargin[1] = 0;
	m_nRepeatsCount = 1;
	m_bMirrored[0] = m_bMirrored[1] = false;
}

VectorImagePanel::~VectorImagePanel()
{
	DestroyTexture();
}

void VectorImagePanel::SetTexture( const char *szFilePath )
{
	if ( !szFilePath )
		return;

	DestroyTexture();
	
	g_pMatSystemSurface->OverrideProportionalBase( m_iBaseResolutionOverride[0], m_iBaseResolutionOverride[1] );
	
#ifndef _DEBUG
	FileHandle_t file = g_pFullFileSystem->Open( szFilePath, "rt" );
	if ( !file )
	{
		Warning( "VectorImagePanel::SetTexture: %s failed to open file \"%s\".\n", GetName(), szFilePath );
		return;
	}

	auto nSize = g_pFullFileSystem->Size( file );
	auto nBufferSize = nSize + 1;
	char* pFileData = (char*) malloc( nSize );
	int bytesRead = g_pFullFileSystem->ReadEx( pFileData, nBufferSize, nSize, file );
	pFileData[bytesRead] = 0;
	g_pFullFileSystem->Close( file );

	std::unique_ptr<lunasvg::Document> document = lunasvg::Document::loadFromData( pFileData );
	free( pFileData );

	if ( !document )
	{
		Warning( "VectorImagePanel::SetTexture: %s: failed to load file \"%s\".\n", GetName(), szFilePath );
		return;
	}

	lunasvg::Bitmap bitmap = document->renderToBitmap(m_iPanelSize[0], m_iPanelSize[1]);
	if ( !bitmap.valid() )
	{
		Warning( "VectorImagePanel::SetTexture: %s: failed to render file \"%s\".\n", GetName(), szFilePath );
		return;
	}
	bitmap.convertToRGBA();

	int wide = bitmap.width();
	int tall = bitmap.height();

	m_nTextureID = surface()->CreateNewTextureID( true );
	surface()->DrawSetTextureRGBA(m_nTextureID, bitmap.data(), wide, tall, 1, true);
#else
	Warning( "VectorImagePanel::SetTexture: %s: SVG support disabled in debug mode for file \"%s\".\n", GetName(), szFilePath );

	// Create a simple placeholder texture (32x32 red square)
	int wide = 32;
	int tall = 32;
	unsigned char* placeholderData = new unsigned char[wide * tall * 4];
	for (int i = 0; i < wide * tall * 4; i += 4)
	{
		placeholderData[i] = 255;     // R
		placeholderData[i+1] = 0;     // G
		placeholderData[i+2] = 0;     // B
		placeholderData[i+3] = 255;   // A
	}

	m_nTextureID = surface()->CreateNewTextureID( true );
	surface()->DrawSetTextureRGBA(m_nTextureID, placeholderData, wide, tall, 1, true);
	delete[] placeholderData;
#endif
	SetSize(wide, tall);
	m_iImageSize[0] = wide;
	m_iImageSize[1] = tall;
	
	int textureWide, textureTall;
	surface()->DrawGetTextureSize( m_nTextureID, textureWide, textureTall );
	m_flTexCoords[0] = m_bMirrored[0] ? (float) wide / (float) textureWide : 0.0f;
	m_flTexCoords[1] = m_bMirrored[1] ? (float) tall / (float) textureTall : 0.0f;
	m_flTexCoords[2] = m_bMirrored[0] ? 0.0f : (float) wide / (float) textureWide;
	m_flTexCoords[3] = m_bMirrored[1] ? 0.0f : (float) tall / (float) textureTall;
}

void VectorImagePanel::DestroyTexture()
{
	if ( m_nTextureID != -1 )
	{
		surface()->DestroyTextureID( m_nTextureID );
		m_nTextureID = -1;
	}
}

void VectorImagePanel::GetImageBaseSize( int &wide, int &tall )
{
	wide = m_iImageSize[0];
	tall = m_iImageSize[1];
}

void VectorImagePanel::GetRenderSize( int &wide, int &tall )
{
	wide = m_iPanelSize[0];
	tall = m_iPanelSize[1];
}

void VectorImagePanel::SetRenderSize( int wide, int tall )
{
	m_iPanelSize[0] = wide;
	m_iPanelSize[1] = tall;
	SetSize(wide, tall);
}

void VectorImagePanel::SetMirrorX( bool bState )
{
	if ( m_bMirrored[0] != bState )
	{
		V_swap( m_flTexCoords[0], m_flTexCoords[2] );
		m_bMirrored[0] = bState;
	}
}

void VectorImagePanel::SetMirrorY( bool bState )
{
	if ( m_bMirrored[1] != bState )
	{
		V_swap( m_flTexCoords[1], m_flTexCoords[3] );
		m_bMirrored[1] = bState;
	}
}

void VectorImagePanel::ApplySettings( KeyValues *inResourceData )
{
	BaseClass::ApplySettings( inResourceData );

	GetSize( m_iPanelSize[0], m_iPanelSize[1] ); // cache the original panel size since its changed in SetTexture below

	const char *szSVGPath = inResourceData->GetString( "image" );
	if ( szSVGPath )
	{
		SetTexture( szSVGPath );
	}

	int alignScreenWide, alignScreenTall;
	surface()->GetScreenSize( alignScreenWide, alignScreenTall );
	ComputePos( this, inResourceData->GetString( "repeat_xpos", NULL ), m_iRepeatMargin[0], m_iPanelSize[0],
				alignScreenWide, m_iBaseResolutionOverride[0], m_iBaseResolutionOverride[1], true, OP_SET );
	ComputePos( this, inResourceData->GetString( "repeat_ypos", NULL ), m_iRepeatMargin[1], m_iPanelSize[1],
				alignScreenTall, m_iBaseResolutionOverride[0], m_iBaseResolutionOverride[1], false, OP_SET );

	m_nRepeatsCount = inResourceData->GetInt( "repeats_count", 1 );

	m_bMirrored[0] = inResourceData->GetBool( "mirror_x" );
	m_bMirrored[1] = inResourceData->GetBool( "mirror_y" );
}

void VectorImagePanel::Paint()
{
	int wide, tall;
	GetSize( wide, tall );

	surface()->DrawSetTexture( m_nTextureID );
	surface()->DrawSetColor( GetFgColor() );
	g_pMatSystemSurface->DisableClipping( true );
	int repeat_x = 0;
	int repeat_y = 0;
	for ( int i = 0; i < m_nRepeatsCount; i++ )
	{
		repeat_x = i * m_iRepeatMargin[0];
		repeat_y = i * m_iRepeatMargin[1];
		surface()->DrawTexturedSubRect(
			repeat_x, repeat_y, 
			repeat_x + wide, repeat_y + tall,
			m_flTexCoords[0], m_flTexCoords[1], 
			m_flTexCoords[2], m_flTexCoords[3]
		);
	}
	g_pMatSystemSurface->DisableClipping( false );
}
