#! /usr/bin/env python
# encoding: utf-8

from waflib import Utils
import os

top = '.'
PROJECT_NAME = 'tier1'

def options(opt):
	# stub
	return

def configure(conf):
	conf.env.append_unique('DEFINES', ['TIER1_STATIC_LIB=1'])

def build(bld):
	source = [
		'../utils/lzma/C/LzmaDec.c',
		'bitbuf.cpp',
		'byteswap.cpp',
		'characterset.cpp',
		'checksum_crc.cpp',
		'checksum_md5.cpp',
		'checksum_sha1.cpp',
		'commandbuffer.cpp',
		'convar.cpp',
		'datamanager.cpp',
		'diff.cpp',
		'generichash.cpp',
		'ilocalize.cpp',
		'interface.cpp',
		'KeyValues.cpp',
		'keyvaluesjson.cpp',
		'kvpacker.cpp',
		'lzmaDecoder.cpp',
		'lzss.cpp', # [!$SOURCESDK]
		'mempool.cpp',
		'memstack.cpp',
		'NetAdr.cpp',
		'newbitbuf.cpp',
		'rangecheckedvar.cpp',
		'reliabletimer.cpp',
		'snappy-sinksource.cpp',
		'snappy-stubs-internal.cpp',
		'snappy.cpp',
		'sparsematrix.cpp',
		'splitstring.cpp',
		'stringpool.cpp',
		'strtools.cpp',
		'strtools_unicode.cpp',
		'tier1.cpp',
		'timeutils.cpp',
		'tokenreader.cpp',
		'uniqueid.cpp',
		'utlbinaryblock.cpp',
		'utlbuffer.cpp',
		'utlbufferutil.cpp',
		'utlsoacontainer.cpp',
		'utlstring.cpp',
		'utlsymbol.cpp'
	]

	if bld.env.DEST_OS == 'win32':
		source += [
			'processor_detect.cpp',
		]
	else:
		source += [
			#'pathmatch.cpp',
			'processor_detect_linux.cpp',
			'qsort_s.cpp',
		]

	includes = [
		'.',
		'../',
		'../public',
		'../public/tier1',
		'../public/tier0',
		'../common'
	]

	defines = []

	libs = []
	if bld.env.DEST_OS == 'win32':
		libs += ['RPCRT4']
	elif bld.env.DEST_OS == "darwin":
		libs += ['ICONV']
		
	bld.stlib(
		source   = source,
		target   = PROJECT_NAME,
		name     = PROJECT_NAME,
		features = 'c cxx',
		includes = includes,
		defines  = defines,
		use      = libs,
		subsystem = bld.env.MSVC_SUBSYSTEM,
		idx      = bld.get_taskgen_count()
	)

