//-----------------------------------------------------------------------------
//	DATAMODEL.VPC
//
//	Project Script
//-----------------------------------------------------------------------------

$Macro SRCDIR		".."

$Include "$SRCDIR\vpc_scripts\source_lib_base.vpc"

$Configuration
{
	$Compiler
	{
		$AdditionalIncludeDirectories			"$SRCDIR\public;$SRCDIR\public\tier0"
		$PreprocessorDefinitions			"$BASE;DATAMODEL_LIB"
		$PrecompiledHeaderFile				"Debug/datamodel.pch"
	}
}

$Project "Datamodel"
{
	$Folder	"Source Files"
	{
		$File	"clipboardmanager.cpp"
		$File	"datamodel.cpp"
		$File	"dependencygraph.cpp"
		$File	"dmattribute.cpp"
		$File	"dmconnect.cpp"
		$File	"dmelement.cpp"
		$File	"dmelementdictionary.cpp"
		$File	"dmelementfactoryhelper.cpp"
		$File	"DmElementFramework.cpp"
		$File	"dmserializerbinary.cpp"
		$File	"dmserializerkeyvalues.cpp"
		$File	"dmserializerkeyvalues2.cpp"
		$File	"undomanager.cpp"
	}

	$Folder	"Header Files"
	{
		$File	"clipboardmanager.h"
		$File	"datamodel.h"
		$File	"dependencygraph.h"
		$File	"dmattributeinternal.h"
		$File	"dmelementdictionary.h"
		$File	"$SRCDIR\public\datamodel\dmelementfactoryhelper.h"
		$File	"DmElementFramework.h"
		$File	"$SRCDIR\public\datamodel\dmelementhandle.h"
		$File	"dmserializerbinary.h"
		$File	"dmserializerkeyvalues.h"
		$File	"dmserializerkeyvalues2.h"
		$File	"undomanager.h"
	}

	$Folder	"external"
	{
		$File	"$SRCDIR\public\tier0\basetypes.h"
		$File	"$SRCDIR\public\tier0\dbg.h"
		$File	"$SRCDIR\public\tier0\fasttimer.h"
		$File	"$SRCDIR\public\appframework\iappsystem.h"
		$File	"$SRCDIR\public\tier0\platform.h"
		$File	"$SRCDIR\public\string_t.h"
	}

	$Folder	"Interface"
	{
		$File	"$SRCDIR\public\datamodel\attributeflags.h"
		$File	"$SRCDIR\public\datamodel\dmattribute.h"
		$File	"$SRCDIR\public\datamodel\dmattributetypes.h"
		$File	"$SRCDIR\public\datamodel\dmattributevar.h"
		$File	"$SRCDIR\public\datamodel\dmehandle.h"
		$File	"$SRCDIR\public\datamodel\dmelement.h"
		$File	"$SRCDIR\public\datamodel\dmvar.h"
		$File	"$SRCDIR\public\datamodel\idatamodel.h"
		$File	"$SRCDIR\public\datamodel\dmxheader.h"
	}
}
